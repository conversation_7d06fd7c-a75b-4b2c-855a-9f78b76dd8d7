#include <iostream>
#include <string>
#include <map>
#include <vector>
#include <sstream> // Added for std::ostringstream

#include "server/HttpServer.h"
#include "server/Request.h"
#include "server/Response.h"
#include "server/Utils.h"
#include "server/Middleware.h"
#include "js_engine/Interpreter.h" // Include the interpreter

int main() {
    HttpServer server(8080);
    Router& router = server.get_router();

    // Enable static file serving from the "public" directory
    server.enableStaticFiles("../public");

    // Define GET routes
    router.get("/", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        response.body = "<html><body><h1>Hello from C++ Web Server!</h1><p>This is a basic page served by a C++ application.</p><p>Path: " + req.path + "</p><p>Visit <a href=\"/index.html\">/index.html</a> for a static page.</p><p>Try the <a href=\"/form.html\">form example</a>.</p><p>Run <a href=\"/run_js?code=let%20x%20%3D%2010%20%2B%205%3B%20print(x)%3B\">JS code</a>.</p><p>Test the <a href=\"/js_tester.html\">JS Interpreter in real-time</a>.</p></body></html>";
        return response;
    });

    router.get("/about", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        response.body = "<html><body><h1>About Us</h1><p>This is a simple C++ web server.</p></body></html>";
        return response;
    });

    router.get("/greet", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        
        std::string name = "Guest";
        auto it = req.query_params.find("name");
        if (it != req.query_params.end()) {
            name = it->second;
        }

        std::map<std::string, std::string> context;
        context["name"] = name;
        response.body = Utils::render_template("<html><body><h1>Hello, {{name}}!</h1><p>Welcome to our C++ web server.</p><p>Try: <a href=\"/greet?name=YourName\">/greet?name=YourName</a></p></body></html>", context);
        return response;
    });

    // New route for running JS code
    router.get("/run_js", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/plain"; // Return plain text output

        std::cout << "[DEBUG] /run_js route called" << std::endl;
        std::cout << "[DEBUG] Query params count: " << req.query_params.size() << std::endl;
        for (const auto& param : req.query_params) {
            std::cout << "[DEBUG] Query param: '" << param.first << "' = '" << param.second << "'" << std::endl;
        }

        auto it = req.query_params.find("code");
        if (it != req.query_params.end()) {
            std::string js_code = it->second;
            Interpreter interpreter;
            std::string interpreter_error_output;
            
            // Define the print callback
            auto print_callback = [&](const std::string& text) {
                std::cout << "[DEBUG] Print callback called with: '" << text << "'" << std::endl;
                response.body += text;
            };

            try {
                std::cout << "[DEBUG] About to interpret code: '" << js_code << "'" << std::endl;
                std::cout << "[DEBUG] Code length: " << js_code.length() << std::endl;
                interpreter.interpret(js_code, print_callback);
                std::cout << "[DEBUG] Interpretation completed. Response body: '" << response.body << "'" << std::endl;
            } catch (const std::runtime_error& e) {
                std::cout << "[DEBUG] Runtime error: " << e.what() << std::endl;
                interpreter_error_output = "Error: " + std::string(e.what()) + "\n";
            } catch (const std::exception& e) {
                std::cout << "[DEBUG] Exception: " << e.what() << std::endl;
                interpreter_error_output = "Error: " + std::string(e.what()) + "\n";
            } catch (...) {
                std::cout << "[DEBUG] Unknown exception occurred" << std::endl;
                interpreter_error_output = "Error: Unknown exception\n";
            }

            if (!interpreter_error_output.empty()) {
                response.body = interpreter_error_output;
            }

        } else {
            response.body = "Error: No 'code' query parameter provided. Example: /run_js?code=let%20x%20%3D%2010%20%2B%205%3B%20print(x)%3B";
        }
        return response;
    });

    // Define POST routes
    router.post("/submit_form", [](const HttpRequest& req) {
        HttpResponse response;
        response.status_code = 200;
        response.status_message = "OK";
        response.headers["Content-Type"] = "text/html";
        
        std::ostringstream oss;
        oss << "<html><body><h1>Form Submission Received!</h1>";
        oss << "<p>You submitted:</p><ul>";
        for (const auto& pair : req.form_data) {
            oss << "<li><strong>" << pair.first << ":</strong> " << pair.second << "</li>";
        }
        oss << "</ul><p>Go back to <a href=\"/form.html\">form</a> or <a href=\"/\">home</a>.</p></body></html>";
        response.body = oss.str();
        return response;
    });

    server.start();

    return 0;
}
